import { useEffect, useRef } from 'react';
import { useSessionTimeoutStore } from './sessionStore';
import { useAuthStore } from './authStore';

/**
 * Hook to trigger an action (e.g., popup) after a timeout when betshopSettings changes.
 * @param onTimeout Callback to execute when timeout completes
 * @param getTimeoutMs Function to extract timeout duration (ms) from betshopSettings
 */
export function useBetshopSettingsTimeout(
    onTimeout: () => void,
    getTimeoutMs: (settings: any) => number
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    useEffect(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        // Only run session timeout logic if user is authenticated
        if (isAuthenticated && betshopSettings) {
            // getTimeoutMs should return minutes, convert to ms
            const minutes = getTimeoutMs(betshopSettings);
            const ms = minutes * 60 * 1000;
            if (ms && ms > 0) {
                timeoutRef.current = setTimeout(() => {
                    onTimeout();
                }, ms);
            }
        }
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [isAuthenticated, betshopSettings, onTimeout, getTimeoutMs]);
}

