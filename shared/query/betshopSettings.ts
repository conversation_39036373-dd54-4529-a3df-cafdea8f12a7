// shared/hooks/business/useBetshopSettings.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { useSessionTimeoutStore } from '@/shared/stores/sessionStore';

interface BetshopSettingsRequest {
    tenantID: number;
}

interface BetshopSettingsItem {
    id: string;
    key: string;
    value: string;
    description?: string;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
}

interface BetshopSettingsResponse {
    success: number;
    message: string;
    data: {
        settingsDetails: BetshopSettingsItem[];
    };
    errors?: string[];
}

interface BetshopSettingsError {
    success: number;
    message: string;
    errors?: string[];
}

/**
 * Fetch betshop settings from the reporting API
 */
export const fetchBetshopSettings = async (tenantId: number, AuthToken?: string): Promise<BetshopSettingsResponse> => {
    const token = useAuthStore.getState().token || AuthToken;

    if (!token && !AuthToken) {
        throw new Error('Authentication token is required');
    }

    if (!tenantId) {
        throw new Error('Tenant ID is required');
    }

    // Use the reporting API URL
    const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

    const requestBody: BetshopSettingsRequest = {
        tenantID: tenantId
    };

    const response = await fetch(`${baseUrl}/api/v2/admin/betshop-settings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${AuthToken || token}`,
        },
        body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
        const errorData: BetshopSettingsError = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data: BetshopSettingsResponse = await response.json();
    if (data?.data?.settingsDetails?.length <= 0) {
        throw new Error(data.message || 'Failed to fetch betshop settings');
    }

    return data;
};

/**
 * Utility function to fetch betshop settings (not a React hook)
 */
export const BetshopSettings = async ({ tenantId, AuthToken }: { tenantId?: number, AuthToken?: string }): Promise<BetshopSettingsResponse | undefined> => {
    const { user } = useAuthStore.getState();
    const effectiveTenantId = tenantId || user?.tenantId;
    const { setTenantId, setBetshopSettings } = useSessionTimeoutStore.getState();

    if (!effectiveTenantId) {
        // No tenantId, return undefined
        return undefined;
    }
    setTenantId(effectiveTenantId);
    const result = await fetchBetshopSettings(effectiveTenantId, AuthToken);
    if (result?.data?.settingsDetails) {
        const cashierWebTimeout = result.data.settingsDetails.find((setting) => setting.key === 'cashier_web_timeout');
        // Store timeout in minutes, not ms
        let timeoutValue: number = cashierWebTimeout?.value ? Number(cashierWebTimeout.value) : 0;
        setBetshopSettings(timeoutValue);
    }
    return result;
};

