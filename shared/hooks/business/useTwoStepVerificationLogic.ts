// shared/hooks/useTwoStepVerificationLogic.ts
'use client';

import React, { useState, useEffect, useRef, useCallback, KeyboardEvent, ClipboardEvent } from 'react'; // Added ClipboardEvent
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/shared/stores/authStore';
import { useValidatePinMutation } from '@/shared/query/mutations/useValidatePinMutation';
import { encodePin } from '@/shared/utils/passwordEncryption';
import { BetshopSettings } from '@/shared/query/betshopSettings';

interface UseTwoStepVerificationLogicReturn {
    inputValues: Record<string, string>;
    setRef: (element: HTMLInputElement | null, index: number) => void;
    handleChange: (index: number, value: string) => void;
    handleKeyDown: (index: number, e: KeyboardEvent<HTMLInputElement>) => void;
    handlePaste: (index: number, e: ClipboardEvent<HTMLInputElement>) => void; // New paste handler
    handleVerifyPin: (e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>) => void;
    isPending: boolean;
    isError: boolean;
    PIN_LENGTH: number;
    error: Error | null;
    twoStepVerificationRequired: boolean;
    tempUserFor2FA: { id: number; email: string } | null;
    loginMessage: string | null;
}

export const useTwoStepVerificationLogic = (): UseTwoStepVerificationLogicReturn => {
    const router = useRouter();
    const { tempUserFor2FA, user, token, twoStepVerificationRequired, isAuthenticated, loginMessage, clearTwoStep } = useAuthStore();
    const { mutate, isPending, isError, error } = useValidatePinMutation();

    const PIN_LENGTH = 6; // Define PIN length as a constant

    const [inputValues, setInputValues] = useState<Record<string, string>>(
        Object.fromEntries(Array.from({ length: PIN_LENGTH }, (_, i) => [String(i), ""]))
    );

    const inputRefs = useRef<Array<HTMLInputElement | null>>(Array(PIN_LENGTH).fill(null));

    // Ref initialization (still needed for initial nulls)
    useEffect(() => {
        inputRefs.current = inputRefs.current.slice(0, PIN_LENGTH);
        for (let i = 0; i < PIN_LENGTH; i++) {
            inputRefs.current[i] = inputRefs.current[i] || null;
        }
    }, []);

    const setRef = useCallback((element: HTMLInputElement | null, index: number) => {
        inputRefs.current[index] = element;
    }, []);

    // Redirection Logic
    useEffect(() => {
        if (isAuthenticated && user) {
            router.replace('/dashboard/');
        }
        if (token) {
            BetshopSettings({ AuthToken: token });
        }
    }, [isAuthenticated, twoStepVerificationRequired, user, token, tempUserFor2FA, router]);

    // Clear 2FA state if component unmounts
    useEffect(() => {
        return () => {
            if (!isAuthenticated) {
                clearTwoStep();
            }
        };
    }, [isAuthenticated, clearTwoStep]);

    // --- Core Logic for Input Handling ---
    const handlePaste = useCallback((index: number, e: ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault(); // Prevent default paste behavior
        const pasteData = e.clipboardData.getData('text').replace(/[^a-zA-Z0-9]/g, '').slice(0, PIN_LENGTH); // Clean and limit pasted data

        const newValues: Record<string, string> = { ...inputValues };
        let currentFillIndex = index;

        for (let i = 0; i < pasteData.length; i++) {
            if (currentFillIndex < PIN_LENGTH) {
                newValues[String(currentFillIndex)] = pasteData[i];
                currentFillIndex++;
            } else {
                break;
            }
        }
        setInputValues(newValues);

        // Focus the last filled input, or the very last input if all filled
        const targetFocusIndex = Math.min(PIN_LENGTH - 1, currentFillIndex - 1);
        inputRefs.current[targetFocusIndex]?.focus();
        if (targetFocusIndex === PIN_LENGTH - 1) {
            inputRefs.current[PIN_LENGTH - 1]?.select(); // Select content of the last input for better UX on paste
        }

    }, [inputValues, inputRefs]);

    const handleVerifyPin = useCallback((e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement> | React.KeyboardEvent<HTMLInputElement>) => { // Adjusted type for KeyboardEvent
        e.preventDefault();
        if (!tempUserFor2FA) {
            router.replace('/authentication/sign-in/');
            return;
        }

        const pin = Object.values(inputValues).join('');
        if (pin.length !== PIN_LENGTH) {
            return;
        }

        // Encode the PIN with base64 before sending to the backend
        const encodedPin = encodePin(pin);

        mutate({
            pin: encodedPin,
            id: tempUserFor2FA.id,
            email: tempUserFor2FA.email,
        });
    }, [inputValues, mutate, tempUserFor2FA, router]);

    const handleChange = useCallback((index: number, value: string) => {
        // Remove non-alphanumeric characters, and take only the first char if multiple
        const cleanedValue = value.replace(/[^a-zA-Z0-9]/g, '');

        // Handle pasting: if value is longer than 1 character
        if (cleanedValue.length > 1) {
            const pastedText = cleanedValue.slice(0, PIN_LENGTH - index); // Get portion that fits from current index
            const newValues: Record<string, string> = { ...inputValues };
            let currentFillIndex = index;

            for (let i = 0; i < pastedText.length; i++) {
                if (currentFillIndex < PIN_LENGTH) {
                    newValues[String(currentFillIndex)] = pastedText[i];
                    currentFillIndex++;
                } else {
                    break;
                }
            }
            setInputValues(newValues);

            // Focus the last filled input, or the very last input if all filled
            const targetFocusIndex = Math.min(PIN_LENGTH - 1, currentFillIndex - 1);
            inputRefs.current[targetFocusIndex]?.focus();
            if (targetFocusIndex === PIN_LENGTH - 1 && newValues[String(PIN_LENGTH - 1)] !== '') {
                inputRefs.current[PIN_LENGTH - 1]?.select(); // Select last input for potential overwriting
            }

        } else if (cleanedValue.length === 1) { // Single character typed
            setInputValues((prevValues) => ({
                ...prevValues,
                [index]: cleanedValue,
            }));
            // Auto-focus next input
            if (index < PIN_LENGTH - 1) {
                inputRefs.current[index + 1]?.focus();
            }
        } else if (cleanedValue.length === 0) { // Character deleted (e.g., retyping over a filled box)
            setInputValues((prevValues) => ({
                ...prevValues,
                [index]: "",
            }));
            // Do not move focus back here, handle in onKeyDown for backspace specifically
        }
    }, [inputValues, inputRefs]);

    const handleKeyDown = useCallback((index: number, e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace') {
            // If current input is empty, move focus to previous input and delete its content
            if (inputValues[index] === '' && index > 0) {
                setInputValues((prevValues) => ({
                    ...prevValues,
                    [index - 1]: "", // Clear previous input's value
                }));
                inputRefs.current[index - 1]?.focus();
            } else if (inputValues[index] !== '') {
                // If current input has content, clear it but don't move focus
                setInputValues((prevValues) => ({
                    ...prevValues,
                    [index]: "",
                }));
            }
            e.preventDefault(); // Prevent default backspace behavior to control it manually
        } else if (e.key === 'ArrowLeft' && index > 0) {
            e.preventDefault();
            inputRefs.current[index - 1]?.focus();
        } else if (e.key === 'ArrowRight' && index < PIN_LENGTH - 1) {
            e.preventDefault();
            inputRefs.current[index + 1]?.focus();
        } else if (e.key === 'Enter' && Object.values(inputValues).join('').length === PIN_LENGTH && !isPending) {
            // Auto-submit on Enter if PIN is full
            handleVerifyPin(e as any); // Cast event to match signature
        }
    }, [inputValues, inputRefs, isPending, handleVerifyPin]);

    return {
        inputValues,
        setRef,
        handleChange,
        handleKeyDown,
        handlePaste, // Expose the new paste handler
        handleVerifyPin,
        isPending,
        isError,
        PIN_LENGTH,
        error,
        twoStepVerificationRequired,
        tempUserFor2FA,
        loginMessage,
    };
};
